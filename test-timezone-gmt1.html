<!DOCTYPE html>
<html>
<head>
    <title>MyFXBook Economic Calendar Test - GMT+1</title>
</head>
<body>
    <!-- Timezone selector with GMT+1 selected -->
    <select name="timezoneoffset" class="form-control" id="timezoneoffset">
        <option value="-12.0" title="GMT -12:00">(GMT -12:00) International Date Line West</option>
        <option value="-11.0" title="GMT -11:00">(GMT -11:00) Coordinated Universal Time</option>
        <option value="-10.0" title="GMT -10:00">(GMT -10:00) Hawaii</option>
        <option value="-9.0" title="GMT -9:00">(GMT -9:00) Alaska</option>
        <option value="-8.0" title="GMT -8:00">(GMT -8:00) Pacific Time (US & Canada)</option>
        <option value="-7.0" title="GMT -7:00">(GMT -7:00) Mountain Time (US & Canada)</option>
        <option value="-6.0" title="GMT -6:00">(GMT -6:00) Central Time (US & Canada), Mexico City</option>
        <option value="-5.0" title="GMT -5:00">(GMT -5:00) Eastern Time (US & Canada), Bogota, Lima</option>
        <option value="-4.0" title="GMT -4:00">(GMT -4:00) Atlantic Time (Canada), La Paz, Santiago</option>
        <option value="-3.0" title="GMT -3:00">(GMT -3:00) Brazil, Buenos Aires</option>
        <option value="-2.0" title="GMT -2:00">(GMT -2:00) Mid-Atlantic</option>
        <option value="-1.0" title="GMT -1:00">(GMT -1:00) Azores, Cape Verde Islands</option>
        <option value="0.0" title="GMT">(GMT) Western Europe Time, London, Lisbon, Casablanca</option>
        <option value="1.0" title="GMT +1:00" selected="selected">(GMT +1:00) Brussels, Copenhagen, Madrid, Paris</option>
        <option value="2.0" title="GMT +2:00">(GMT +2:00) South Africa, Jerusalem, Kaliningrad</option>
        <option value="3.0" title="GMT +3:00">(GMT +3:00) Moscow, St. Petersburg, Baghdad, Riyadh</option>
        <option value="9.0" title="GMT +9:00">(GMT +9:00) Yakutsk, Tokyo, Seoul, Osaka, Sapporo</option>
    </select>

    <!-- Sample economic calendar table -->
    <table>
        <tr>
            <td>Jun 30, 13:00</td>
            <td></td>
            <td>
                <div class="display-flex-align-items-center gap-5">
                    <i class="United States align-center" title="United States">
                        <span class="flag flag-icon-us"></span>
                    </i>
                </div>
            </td>
            <td>USD</td>
            <td>Fed Chair Powell Speech</td>
            <td>High</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td>Jun 30, 14:30</td>
            <td></td>
            <td>
                <div class="display-flex-align-items-center gap-5">
                    <i class="United States align-center" title="United States">
                        <span class="flag flag-icon-us"></span>
                    </i>
                </div>
            </td>
            <td>USD</td>
            <td>GDP Growth Rate QoQ (Q1)</td>
            <td>High</td>
            <td>1.3%</td>
            <td>1.4%</td>
            <td>1.4%</td>
            <td></td>
        </tr>
        <tr>
            <td>Jun 30, 01:00</td>
            <td></td>
            <td>
                <div class="display-flex-align-items-center gap-5">
                    <i class="Euro Area align-center" title="Euro Area">
                        <span class="flag flag-icon-eu"></span>
                    </i>
                </div>
            </td>
            <td>EUR</td>
            <td>Inflation Rate YoY (Jun)</td>
            <td>High</td>
            <td>2.5%</td>
            <td>2.6%</td>
            <td>2.6%</td>
            <td></td>
        </tr>
    </table>
</body>
</html>
