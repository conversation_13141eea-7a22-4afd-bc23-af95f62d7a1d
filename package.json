{"name": "simple-trade-tracker", "version": "0.1.0", "homepage": "/", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@firebase/auth": "^1.10.0", "@mui/icons-material": "^7.0.1", "@mui/material": "^7.0.1", "@mui/x-date-pickers": "^7.28.3", "@supabase/supabase-js": "^2.50.5", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "@types/draft-js": "^0.11.18", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@types/uuid": "^10.0.0", "@xenova/transformers": "^2.17.2", "axios": "^1.10.0", "cheerio": "^1.1.0", "date-fns": "^2.29.3", "dotenv": "^17.2.0", "draft-js": "^0.11.7", "draft-js-export-html": "^1.4.1", "firebase": "^11.10.0", "firebase-admin": "^13.2.0", "firebase-functions": "^6.3.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.4.1", "react-scripts": "5.0.1", "recharts": "^2.15.2", "tailwind-merge": "^3.2.0", "typescript": "^4.9.5", "uuid": "^11.1.0", "web-vitals": "^2.1.4", "xlsx": "^0.18.5"}, "scripts": {"predeploy": "npm run build", "deploy": "gh-pages -d build", "start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "migrate-events": "npx ts-node src/scripts/backfillTradeEconomicEvents.ts", "migrate-trade-events": "node migrate-trade-economic-events.js", "setup-vector-index": "node scripts/setup-vector-index.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/cheerio": "^0.22.35", "firebase-tools": "^14.9.0", "gh-pages": "^6.3.0", "source-map-explorer": "^2.5.3", "ts-node": "^10.9.2"}}