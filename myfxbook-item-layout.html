<tr id="calRow279494" data-row-id="279494" class="economicCalendarRow" role="button">
    <td class="calendarToggleCell">
        <input type="hidden" id="itemOid" value="279494" style="cursor: pointer;">
        <div class="align-center calendarDateTd" style="width: 90px;" data-calendardatetd="2025-06-17 12:30:00.0">
            Jun 17, 13:30
        </div>
    </td>
    
    <td class="calendarToggleCell no-padding">
        <div>
            <span name="calendarLeft" class="calendarLeft text-center" data-is-holiday="false" importance="3"
                time="1750163400000" style="display: none" readonly="readonly" ispassed="1"></span>
        </div>
        <div class="font-green-jungle calendarEventDone">
            <i class="fas fa-check green"></i>
        </div>
        <div style=" display: none ">
            <i class="fas fa-umbrella-beach color-black"></i>
        </div>
    </td>
    <td class="calendarToggleCell">
        <div class="display-flex-align-items-center gap-5">
            <i id="calendarTip$rowCounter" class="United States align-center" title="United States">
                <span class="flag flag-icon-us"></span>
            </i>
        </div>

    </td>
    <td class="calendarToggleCell">
        USD
    </td>
    <td class="calendarToggleCell text-left">
        <a href="https://www.myfxbook.com/forex-economic-calendar/united-states/retail-sales-ex-gas-autos-mom"
            class="calendar-event-link" target="_blank">Retail Sales Ex
            Gas/Autos MoM</a>
        <span>(May)</span>
    </td>
    <td class="calendarToggleCell">
        <div class="impact_high">
            High
        </div>
    </td>
    <td data-previous="279494" pot="%" unit="" previous-value="0.1" relation="true"
        class="background-transparent-red  calendarToggleCell">
        <span class="previousCell">
            <span class="eventTdPopover dotted" data-original-title="Revised" data-content="Revised from a previous value of 0.2%
                              (-0.1%)">
                0.1%
            </span>
        </span>
    </td>
    <td data-concensus="279494" concensus="0.2" consistconcensus="true" class="calendarToggleCell">
        <div class="align-center">
            0.2%
        </div>
    </td>
    <td data-actual="279494" pot="%" unit="" class="background-transparent-red calendarToggleCell">
        <span class="actualCell">
            <span class="eventTdPopover dotted" data-original-title="Actual"
                data-content="A worse than expected result (-0.3%)">
                -0.1%
            </span>
        </span>
    </td>
    <td data-ignoreresponsiveclick="false" class="calendarAllNoneColumn calendarToggleCell">
    </td>
</tr>