rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow read/write access on all documents to any user signed in to the application
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
    
    // Allow read access to economic calendar cache for all users
    match /economicCalendarCache/{document} {
      allow read: if true;
      allow write: if false; // Only functions can write to cache
    }

    // Allow read access to economic events for all users
    match /economicEvents/{document} {
      allow read: if true;
      allow write: if false; // Only functions can write events
    }
  }
}
