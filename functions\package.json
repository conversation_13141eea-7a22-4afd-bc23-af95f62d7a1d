{"name": "functions", "scripts": {"build": "tsc", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "main": "lib/index.js", "dependencies": {"cheerio": "^1.0.0-rc.12", "crypto": "^1.0.1", "firebase-admin": "^11.11.1", "firebase-functions": "^6.3.2"}, "devDependencies": {"typescript": "^4.9.0"}, "private": true}